# Project Settings and Configuration

## Strategy Parameters

### Session Timings (UTC)
```go
AsianStartHour:  0    // 00:00 UTC
AsianEndHour:    8    // 08:00 UTC  
LondonStartHour: 8    // 08:00 UTC
LondonEndHour:   16   // 16:00 UTC
```

### Trading Parameters
```go
BreakoutBuffer:  2.0  // 2 pips minimum breakout distance
StopLossPips:    20.0 // 20 pip stop loss
TakeProfitPips:  40.0 // 40 pip take profit (2:1 RR ratio)
MaxPositions:    2    // Maximum concurrent positions
```

### Memory Management Settings
```go
TickBufferSize:     1000    // Reduced from 10,000 for memory efficiency
CSVBufferSize:      4096    // 4KB streaming buffer
CleanupInterval:    100000  // Memory cleanup every 100,000 ticks
ProgressInterval:   1000000 // Progress reporting every 1M ticks
```

## File Paths and Structure

### Data Directory Structure
```
CSV-DATA/
├── BONDS/
├── COMMODITIES/
├── CRYPTO/
├── CURRENCIES/
├── INDEXES/
└── METALS/
```

### Configuration Files
- `KeyFile.json`: Instrument definitions and file paths
- `test_config.json`: Test configuration for development
- `trading_results.log`: Output file for backtest results

### Data File Format
```
Time (UTC),Ask,Bid,AskVolume,BidVolume
2019.01.01 22:02:37.254,1.14682,1.14598,0.75,3.75
```

## Instrument Categories and Pip Values

### Forex Pairs (5 decimal places)
- Major pairs: 0.0001 pip value
- Examples: EURUSD, GBPUSD, USDCAD, AUDUSD

### Forex Pairs (3 decimal places)  
- JPY pairs: 0.01 pip value
- Examples: USDJPY, GBPJPY, EURJPY

### Commodities
- Most: 0.01 pip value (3 decimal places)
- Some: 0.001 pip value (4 decimal places)

### Indices
- Varies by index: 0.01 to 1.0 pip value
- Decimal places: 0-3

### Bonds
- Typically: 0.01 pip value (3 decimal places)

### Metals
- Gold/Silver: 0.01 pip value (3 decimal places)

### Crypto
- Bitcoin: 0.1 pip value (1 decimal place)

## Environment Variables
No environment variables currently required. All configuration is file-based.

## Build Settings
```bash
# Standard Go build
go run main.go KeyFile.json

# For production builds
go build -o strategy-tester main.go
./strategy-tester KeyFile.json
```

## Memory Optimization Settings

### Garbage Collection
- Automatic GC every 100,000 ticks
- Manual GC calls during daily state transitions
- Buffer clearing on memory cleanup cycles

### Buffer Management
- Tick buffer: 1,000 tick capacity
- CSV reader: 4KB buffer size
- Position slice: Pre-allocated with capacity 10

### Progress Monitoring
- Memory usage reported every 1M ticks
- Processing speed tracking
- Final memory statistics in results

## Output Format

### Log File Structure
```
INSTRUMENT: [CODE]
FILE: [PATH]
TICKS_PROCESSED: [COUNT]
TOTAL_TRADES: [COUNT]
WINNING_TRADES: [COUNT]
LOSING_TRADES: [COUNT]
WIN_RATE: [PERCENTAGE]%
TOTAL_PNL: [VALUE] pips
AVG_WIN: [VALUE] pips
AVG_LOSS: [VALUE] pips
MAX_PROFIT: [VALUE] pips
MAX_DRAWDOWN: [VALUE] pips
MEMORY_USED: [VALUE] MB
PROCESSING_TIME: [TIMESTAMP]
```

## Performance Targets
- Memory usage: < 1 MB per instrument
- Processing speed: > 1M ticks per second
- Constant memory footprint regardless of dataset size
