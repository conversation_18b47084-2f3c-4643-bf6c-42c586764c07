[{"instrument_code": "BUNDTREUR", "instrument_name": "German Bund (EUR)", "category": "BONDS", "file_path": "CSV-DATA/BONDS/BUNDTREUR_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "UKGILTTRGBP", "instrument_name": "UK Gilt (GBP)", "category": "BONDS", "file_path": "CSV-DATA/BONDS/UKGILTTRGBP_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "USTBONDTRUSD", "instrument_name": "US Treasury Bond (USD)", "category": "BONDS", "file_path": "CSV-DATA/BONDS/USTBONDTRUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "BRENTCMDUSD", "instrument_name": "Brent Crude Oil (USD)", "category": "COMMODITIES", "file_path": "CSV-DATA/COMMODITIES/BRENTCMDUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "COPPERCMDUSD", "instrument_name": "Copper (USD)", "category": "COMMODITIES", "file_path": "CSV-DATA/COMMODITIES/COPPERCMDUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 4, "effective_pip_value": 0.001}, {"instrument_code": "GASCMDUSD", "instrument_name": "Natural Gas (USD)", "category": "COMMODITIES", "file_path": "CSV-DATA/COMMODITIES/GASCMDUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 4, "effective_pip_value": 0.001}, {"instrument_code": "LIGHTCMDUSD", "instrument_name": "Light Sweet Crude Oil (WTI)", "category": "COMMODITIES", "file_path": "CSV-DATA/COMMODITIES/LIGHTCMDUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "OJUICECMDUSX", "instrument_name": "Orange Juice (US Cents)", "category": "COMMODITIES", "file_path": "CSV-DATA/COMMODITIES/OJUICECMDUSX_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "SOYBEANCMDUSX", "instrument_name": "Soybeans (US Cents)", "category": "COMMODITIES", "file_path": "CSV-DATA/COMMODITIES/SOYBEANCMDUSX_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "BTCUSD", "instrument_name": "Bitcoin vs. US Dollar", "category": "CRYPTO", "file_path": "CSV-DATA/CRYPTO/BTCUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 1, "effective_pip_value": 0.1}, {"instrument_code": "AUDUSD", "instrument_name": "Australian Dollar vs. US Dollar", "category": "CURRENCIES", "file_path": "CSV-DATA/CURRENCIES/AUDUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 5, "effective_pip_value": 0.0001}, {"instrument_code": "EURGBP", "instrument_name": "Euro vs. British Pound", "category": "CURRENCIES", "file_path": "CSV-DATA/CURRENCIES/EURGBP_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 5, "effective_pip_value": 0.0001}, {"instrument_code": "EURUSD", "instrument_name": "Euro vs. US Dollar", "category": "CURRENCIES", "file_path": "CSV-DATA/CURRENCIES/EURUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 5, "effective_pip_value": 0.0001}, {"instrument_code": "GBPAUD", "instrument_name": "British Pound vs. Australian Dollar", "category": "CURRENCIES", "file_path": "CSV-DATA/CURRENCIES/GBPAUD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 5, "effective_pip_value": 0.0001}, {"instrument_code": "GBPCAD", "instrument_name": "British Pound vs. Canadian Dollar", "category": "CURRENCIES", "file_path": "CSV-DATA/CURRENCIES/GBPCAD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 5, "effective_pip_value": 0.0001}, {"instrument_code": "GBPCHF", "instrument_name": "British Pound vs. Swiss Franc", "category": "CURRENCIES", "file_path": "CSV-DATA/CURRENCIES/GBPCHF_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 5, "effective_pip_value": 0.0001}, {"instrument_code": "GBPCNH", "instrument_name": "British Pound vs. Chinese Yuan Offshore", "category": "CURRENCIES", "file_path": "CSV-DATA/CURRENCIES/GBPCNH_Ticks_2024.05.15_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 4, "effective_pip_value": 0.001}, {"instrument_code": "GBPJPY", "instrument_name": "British Pound vs. Japanese Yen", "category": "CURRENCIES", "file_path": "CSV-DATA/CURRENCIES/GBPJPY_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "GBPNZD", "instrument_name": "British Pound vs. New Zealand Dollar", "category": "CURRENCIES", "file_path": "CSV-DATA/CURRENCIES/GBPNZD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 5, "effective_pip_value": 0.0001}, {"instrument_code": "GBPUSD", "instrument_name": "British Pound vs. US Dollar", "category": "CURRENCIES", "file_path": "CSV-DATA/CURRENCIES/GBPUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 5, "effective_pip_value": 0.0001}, {"instrument_code": "NZDUSD", "instrument_name": "New Zealand Dollar vs. US Dollar", "category": "CURRENCIES", "file_path": "CSV-DATA/CURRENCIES/NZDUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 4, "effective_pip_value": 0.001}, {"instrument_code": "USDCAD", "instrument_name": "US Dollar vs. Canadian Dollar", "category": "CURRENCIES", "file_path": "CSV-DATA/CURRENCIES/USDCAD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 5, "effective_pip_value": 0.0001}, {"instrument_code": "USDCHF", "instrument_name": "US Dollar vs. Swiss Franc", "category": "CURRENCIES", "file_path": "CSV-DATA/CURRENCIES/USDCHF_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 5, "effective_pip_value": 0.0001}, {"instrument_code": "USDCNH", "instrument_name": "US Dollar vs. Chinese Yuan Offshore", "category": "CURRENCIES", "file_path": "CSV-DATA/CURRENCIES/USDCNH_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 5, "effective_pip_value": 0.0001}, {"instrument_code": "USDJPY", "instrument_name": "US Dollar vs. Japanese Yen", "category": "CURRENCIES", "file_path": "CSV-DATA/CURRENCIES/USDJPY_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "AUSIDXAUD", "instrument_name": "Australia 200 Index (AUD)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/AUSIDXAUD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "CHEIDXCHF", "instrument_name": "Switzerland 20 Index (SMI)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/CHEIDXCHF_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 2, "effective_pip_value": 0.1}, {"instrument_code": "CHIIDXUSD", "instrument_name": "China A50 Index (USD)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/CHIIDXUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 2, "effective_pip_value": 0.1}, {"instrument_code": "DEUIDXEUR", "instrument_name": "Germany 40 Index (DAX)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/DEUIDXEUR_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 2, "effective_pip_value": 0.1}, {"instrument_code": "DOLLARIDXUSD", "instrument_name": "US Dollar Index (USD)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/DOLLARIDXUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "ESPIDXEUR", "instrument_name": "Spain 35 Index (IBEX 35)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/ESPIDXEUR_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "EUSIDXEUR", "instrument_name": "EU Stocks 50 Index (Euro Stoxx 50)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/EUSIDXEUR_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 2, "effective_pip_value": 0.1}, {"instrument_code": "FRAIDXEUR", "instrument_name": "France 40 Index (CAC 40)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/FRAIDXEUR_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "GBRIDXGBP", "instrument_name": "UK 100 Index (FTSE 100)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/GBRIDXGBP_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 2, "effective_pip_value": 0.1}, {"instrument_code": "HKGIDXHKD", "instrument_name": "Hong Kong 50 Index (<PERSON>)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/HKGIDXHKD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 2, "effective_pip_value": 0.1}, {"instrument_code": "ITAIDXEUR", "instrument_name": "Italy 40 Index (FTSE MIB)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/ITAIDXEUR_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 0, "effective_pip_value": 1}, {"instrument_code": "JPNIDXJPY", "instrument_name": "Japan 225 Index (Nikkei 225)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/JPNIDXJPY_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 2, "effective_pip_value": 0.1}, {"instrument_code": "NLDIDXEUR", "instrument_name": "Netherlands 25 Index (AEX)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/NLDIDXEUR_Ticks_2019.01.01_2025.08.03..csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "USA30IDXUSD", "instrument_name": "US 30 Index (<PERSON>)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/USA30IDXUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "USA500IDXUSD", "instrument_name": "US 500 Index (S&P 500)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/USA500IDXUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "USATECHIDXUSD", "instrument_name": "US Tech 100 Index (NASDAQ 100)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/USATECHIDXUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "USSC2000IDXUSD", "instrument_name": "US Small Cap 2000 Index (Russell 2000)", "category": "INDEXES", "file_path": "CSV-DATA/INDEXES/USSC2000IDXUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "XAGUSD", "instrument_name": "Silver Spot vs. US Dollar", "category": "METALS", "file_path": "CSV-DATA/METALS/XAGUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}, {"instrument_code": "XAUUSD", "instrument_name": "Gold Spot vs. US Dollar", "category": "METALS", "file_path": "CSV-DATA/METALS/XAUUSD_Ticks_2019.01.01_2025.08.03.csv", "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume", "quoting_decimal_places": 3, "effective_pip_value": 0.01}]