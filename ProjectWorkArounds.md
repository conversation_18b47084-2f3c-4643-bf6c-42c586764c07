# Project Workarounds and Issues

## Memory Management Issues (RESOLVED)

### Problem
The original implementation had severe memory leaks:
- `CompletedTrades` slice grew indefinitely, storing every trade for years of data
- No rolling window processing despite having a `tickBuffer`
- CSV reader buffered entire files in memory
- No periodic memory cleanup
- Memory usage grew linearly with dataset size

### Solution
Implemented comprehensive memory management:
- Replaced `CompletedTrades` with `AggregatedStats` for constant memory usage
- Added periodic memory cleanup every 100,000 ticks
- Reduced CSV buffer size to 4KB for streaming processing
- Implemented `performMemoryCleanup()` function with garbage collection
- Clear inactive positions and reset buffers regularly

### Result
- Memory usage reduced from potentially gigabytes to < 1 MB
- Constant memory footprint regardless of dataset size
- Eliminated memory leaks and out-of-memory errors

## Statistics Calculation Issues (RESOLVED)

### Problem
The original `calculateStats()` function had critical errors:
- Calculated theoretical PnL based on take-profit levels instead of actual realized PnL
- This caused impossible results like 100% win rates with massive negative PnL
- Individual positions didn't store actual exit information

### Solution
- Added `RealizedPnL`, `ExitPrice`, `ExitTime`, and `ExitReason` fields to Position struct
- Modified `closePosition()` to store actual PnL when positions are closed
- Updated `calculateStats()` to use aggregated statistics from actual trades
- Implemented proper win/loss counting based on actual realized PnL

### Result
- Accurate and mathematically consistent performance metrics
- Realistic win rates and PnL calculations
- Proper tracking of average wins and losses

## End-of-Day Position Closing (RESOLVED)

### Problem
Original implementation closed positions using dummy tick prices:
```go
lastTick := &Tick{
    Timestamp: time.Now(),
    Ask:       tp.state.AsianRange.High,
    Bid:       tp.state.AsianRange.Low,
    Mid:       (tp.state.AsianRange.High + tp.state.AsianRange.Low) / 2,
}
```
This created unrealistic exit prices and skewed results.

### Solution
- Store `LastTick` in daily state during processing
- Use actual market prices from the last processed tick for end-of-day closing
- Ensure realistic position exits

### Result
- Accurate position closing at actual market prices
- More realistic backtest results
- Eliminated artificial price distortions

## Session Timing Considerations

### Current Implementation
- Asian Session: 00:00 - 08:00 UTC
- London Session: 08:00 - 16:00 UTC

### Potential Optimization
Real trading sessions vary:
- Tokyo: 23:00 - 08:00 UTC (Sunday-Thursday)
- London: 08:00 - 17:00 UTC
- New York: 13:00 - 22:00 UTC

Current implementation is simplified but functional for backtesting purposes.

## Performance Optimizations Applied

1. **Reduced buffer sizes**: From 10,000 to 1,000 ticks for tick buffer
2. **Streaming CSV processing**: 4KB buffer instead of full file buffering
3. **Periodic cleanup**: Every 100,000 ticks to prevent memory accumulation
4. **Garbage collection**: Explicit GC calls during cleanup cycles
5. **Position pruning**: Remove inactive positions to prevent slice growth
6. **Progress monitoring**: Memory usage reporting for performance tracking
