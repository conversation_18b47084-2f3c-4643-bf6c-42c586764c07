package main

import (
	"bufio"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
	"runtime"
	"strconv"
	"time"
)

// Instrument configuration
type Instrument struct {
	InstrumentCode       string  `json:"instrument_code"`
	InstrumentName       string  `json:"instrument_name"`
	Category             string  `json:"category"`
	FilePath             string  `json:"file_path"`
	DataStructure        string  `json:"data_structure"`
	QuotingDecimalPlaces int     `json:"quoting_decimal_places"`
	EffectivePipValue    float64 `json:"effective_pip_value"`
}

// Tick data structure
type Tick struct {
	Timestamp time.Time
	Ask       float64
	Bid       float64
	AskVolume int64
	BidVolume int64
	Mid       float64
}

// Trading session ranges
type SessionRange struct {
	High float64
	Low  float64
	Open float64
}

// Position tracking
type Position struct {
	EntryPrice  float64
	EntryTime   time.Time
	ExitPrice   float64
	ExitTime    time.Time
	IsLong      bool
	Size        float64
	StopLoss    float64
	TakeProfit  float64
	IsActive    bool
	RealizedPnL float64 // Actual PnL in pips when position is closed
	ExitReason  string  // "TP", "SL", "EOD" (Take Profit, Stop Loss, End of Day)
}

// Aggregated statistics (memory efficient)
type AggregatedStats struct {
	TotalTrades   int
	WinningTrades int
	LosingTrades  int
	TotalPnL      float64
	TotalWinPnL   float64
	TotalLossPnL  float64
	MaxDrawdown   float64
	MaxProfit     float64
	WinRate       float64
}

// Strategy result
type StrategyResult struct {
	TotalTrades   int
	WinningTrades int
	LosingTrades  int
	TotalPnL      float64
	MaxDrawdown   float64
	MaxProfit     float64
	WinRate       float64
	AvgWin        float64
	AvgLoss       float64
}

// Daily trading state (memory efficient)
type DailyState struct {
	CurrentDate    string
	AsianRange     SessionRange
	LondonRange    SessionRange
	AsianComplete  bool
	LondonComplete bool
	Positions      []Position
	DailyPnL       float64
	LastTick       *Tick // Store last tick for end-of-day closing
}

// Strategy parameters
type StrategyParams struct {
	AsianStartHour  int     // Asian session start (UTC)
	AsianEndHour    int     // Asian session end (UTC)
	LondonStartHour int     // London session start (UTC)
	LondonEndHour   int     // London session end (UTC)
	BreakoutBuffer  float64 // Minimum breakout distance (in pips)
	StopLossPips    float64 // Stop loss in pips
	TakeProfitPips  float64 // Take profit in pips
	MaxPositions    int     // Maximum concurrent positions
}

// Default strategy parameters
func getDefaultParams() StrategyParams {
	return StrategyParams{
		AsianStartHour:  0,    // 00:00 UTC (Asian session approximation)
		AsianEndHour:    8,    // 08:00 UTC
		LondonStartHour: 8,    // 08:00 UTC (London session)
		LondonEndHour:   16,   // 16:00 UTC
		BreakoutBuffer:  2.0,  // 2 pips minimum breakout
		StopLossPips:    20.0, // 20 pip stop loss
		TakeProfitPips:  40.0, // 40 pip take profit (2:1 RR)
		MaxPositions:    2,    // Max 2 concurrent positions
	}
}

// Tick processor with rolling window and memory management
type TickProcessor struct {
	instrument Instrument
	params     StrategyParams
	state      DailyState
	stats      AggregatedStats // Running statistics across all days
	tickBuffer []Tick
	bufferSize int
	logFile    *os.File
}

func NewTickProcessor(instrument Instrument, params StrategyParams, logPath string) (*TickProcessor, error) {
	logFile, err := os.OpenFile(logPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, fmt.Errorf("failed to open log file: %v", err)
	}

	return &TickProcessor{
		instrument: instrument,
		params:     params,
		state: DailyState{
			Positions: make([]Position, 0),
		},
		stats:      AggregatedStats{},
		tickBuffer: make([]Tick, 0, 1000), // Smaller buffer for memory efficiency
		bufferSize: 1000,
		logFile:    logFile,
	}, nil
}

func (tp *TickProcessor) Close() {
	if tp.logFile != nil {
		tp.logFile.Close()
	}
}

// Parse tick from CSV row
func (tp *TickProcessor) parseTick(record []string) (*Tick, error) {
	if len(record) < 5 {
		return nil, fmt.Errorf("insufficient columns in record")
	}

	// Try multiple timestamp formats
	var timestamp time.Time
	var err error

	// Format 1: "2019.02.05 19:09:17.005" (with milliseconds)
	timestamp, err = time.Parse("2006.01.02 15:04:05.000", record[0])
	if err != nil {
		// Format 2: "2019.02.05 19:09:17" (without milliseconds)
		timestamp, err = time.Parse("2006.01.02 15:04:05", record[0])
		if err != nil {
			// Format 3: "2019-01-02 15:04:05" (standard format)
			timestamp, err = time.Parse("2006-01-02 15:04:05", record[0])
			if err != nil {
				return nil, fmt.Errorf("failed to parse timestamp '%s': %v", record[0], err)
			}
		}
	}

	ask, err := strconv.ParseFloat(record[1], 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse ask: %v", err)
	}

	bid, err := strconv.ParseFloat(record[2], 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse bid: %v", err)
	}

	askVol, err := strconv.ParseInt(record[3], 10, 64)
	if err != nil {
		askVol = 0 // Default to 0 if parsing fails
	}

	bidVol, err := strconv.ParseInt(record[4], 10, 64)
	if err != nil {
		bidVol = 0 // Default to 0 if parsing fails
	}

	return &Tick{
		Timestamp: timestamp,
		Ask:       ask,
		Bid:       bid,
		AskVolume: askVol,
		BidVolume: bidVol,
		Mid:       (ask + bid) / 2.0,
	}, nil
}

// Check if new trading day
func (tp *TickProcessor) isNewDay(tick *Tick) bool {
	currentDate := tick.Timestamp.Format("2006-01-02")
	return tp.state.CurrentDate != currentDate
}

// Initialize new trading day with memory cleanup
func (tp *TickProcessor) initializeNewDay(tick *Tick) {
	// Close all positions from previous day
	tp.closeAllPositions(tick)

	// Clear daily state but preserve aggregated statistics
	tp.state = DailyState{
		CurrentDate:    tick.Timestamp.Format("2006-01-02"),
		AsianRange:     SessionRange{High: tick.Mid, Low: tick.Mid, Open: tick.Mid},
		LondonRange:    SessionRange{High: tick.Mid, Low: tick.Mid, Open: tick.Mid},
		Positions:      make([]Position, 0, 10), // Pre-allocate small capacity
		AsianComplete:  false,
		LondonComplete: false,
		DailyPnL:       0.0,
		LastTick:       tick,
	}

	// Clear tick buffer to free memory
	tp.tickBuffer = tp.tickBuffer[:0]

	// Force garbage collection periodically
	runtime.GC()
}

// Update session ranges
func (tp *TickProcessor) updateSessionRanges(tick *Tick) {
	hour := tick.Timestamp.Hour()

	// Update Asian session range
	if hour >= tp.params.AsianStartHour && hour < tp.params.AsianEndHour {
		if tick.Mid > tp.state.AsianRange.High {
			tp.state.AsianRange.High = tick.Mid
		}
		if tick.Mid < tp.state.AsianRange.Low {
			tp.state.AsianRange.Low = tick.Mid
		}
	} else if hour == tp.params.AsianEndHour && !tp.state.AsianComplete {
		tp.state.AsianComplete = true
	}

	// Update London session range
	if hour >= tp.params.LondonStartHour && hour < tp.params.LondonEndHour {
		if tick.Mid > tp.state.LondonRange.High {
			tp.state.LondonRange.High = tick.Mid
		}
		if tick.Mid < tp.state.LondonRange.Low {
			tp.state.LondonRange.Low = tick.Mid
		}
	}
}

// Check for breakout opportunities
func (tp *TickProcessor) checkBreakouts(tick *Tick) {
	if !tp.state.AsianComplete {
		return
	}

	hour := tick.Timestamp.Hour()
	if hour < tp.params.LondonStartHour || hour >= tp.params.LondonEndHour {
		return
	}

	if len(tp.state.Positions) >= tp.params.MaxPositions {
		return
	}

	bufferPips := tp.params.BreakoutBuffer * tp.instrument.EffectivePipValue

	// Long breakout above Asian high
	if tick.Mid > tp.state.AsianRange.High+bufferPips {
		tp.openPosition(tick, true, tp.state.AsianRange.High)
	}

	// Short breakout below Asian low
	if tick.Mid < tp.state.AsianRange.Low-bufferPips {
		tp.openPosition(tick, false, tp.state.AsianRange.Low)
	}
}

// Open a new position
func (tp *TickProcessor) openPosition(tick *Tick, isLong bool, referenceLevel float64) {
	stopLossDistance := tp.params.StopLossPips * tp.instrument.EffectivePipValue
	takeProfitDistance := tp.params.TakeProfitPips * tp.instrument.EffectivePipValue

	var entryPrice, stopLoss, takeProfit float64

	if isLong {
		entryPrice = tick.Ask
		stopLoss = entryPrice - stopLossDistance
		takeProfit = entryPrice + takeProfitDistance
	} else {
		entryPrice = tick.Bid
		stopLoss = entryPrice + stopLossDistance
		takeProfit = entryPrice - takeProfitDistance
	}

	position := Position{
		EntryPrice: entryPrice,
		EntryTime:  tick.Timestamp,
		IsLong:     isLong,
		Size:       1.0, // Standard position size
		StopLoss:   stopLoss,
		TakeProfit: takeProfit,
		IsActive:   true,
	}

	tp.state.Positions = append(tp.state.Positions, position)
}

// Update existing positions
func (tp *TickProcessor) updatePositions(tick *Tick) {
	for i := range tp.state.Positions {
		if !tp.state.Positions[i].IsActive {
			continue
		}

		position := &tp.state.Positions[i]
		var currentPrice float64

		if position.IsLong {
			currentPrice = tick.Bid // Exit at bid for long positions

			// Check stop loss or take profit
			if currentPrice <= position.StopLoss {
				tp.closePosition(position, currentPrice, tick.Timestamp, "SL")
			} else if currentPrice >= position.TakeProfit {
				tp.closePosition(position, currentPrice, tick.Timestamp, "TP")
			}
		} else {
			currentPrice = tick.Ask // Exit at ask for short positions

			// Check stop loss or take profit
			if currentPrice >= position.StopLoss {
				tp.closePosition(position, currentPrice, tick.Timestamp, "SL")
			} else if currentPrice <= position.TakeProfit {
				tp.closePosition(position, currentPrice, tick.Timestamp, "TP")
			}
		}
	}
}

// Close a position and update aggregated statistics
func (tp *TickProcessor) closePosition(position *Position, exitPrice float64, exitTime time.Time, reason string) {
	var pnl float64

	if position.IsLong {
		pnl = (exitPrice - position.EntryPrice) / tp.instrument.EffectivePipValue
	} else {
		pnl = (position.EntryPrice - exitPrice) / tp.instrument.EffectivePipValue
	}

	// Update position with exit information
	position.IsActive = false
	position.ExitPrice = exitPrice
	position.ExitTime = exitTime
	position.RealizedPnL = pnl
	position.ExitReason = reason

	// Update aggregated statistics (memory efficient)
	tp.stats.TotalTrades++
	tp.stats.TotalPnL += pnl
	tp.state.DailyPnL += pnl

	if pnl > 0 {
		tp.stats.WinningTrades++
		tp.stats.TotalWinPnL += pnl
	} else if pnl < 0 {
		tp.stats.LosingTrades++
		tp.stats.TotalLossPnL += pnl
	}

	// Update drawdown tracking
	if tp.stats.TotalPnL > tp.stats.MaxProfit {
		tp.stats.MaxProfit = tp.stats.TotalPnL
	}

	drawdown := tp.stats.MaxProfit - tp.stats.TotalPnL
	if drawdown > tp.stats.MaxDrawdown {
		tp.stats.MaxDrawdown = drawdown
	}
}

// Close all positions (end of day)
func (tp *TickProcessor) closeAllPositions(tick *Tick) {
	for i := range tp.state.Positions {
		if !tp.state.Positions[i].IsActive {
			continue
		}

		position := &tp.state.Positions[i]
		var exitPrice float64

		if position.IsLong {
			exitPrice = tick.Bid
		} else {
			exitPrice = tick.Ask
		}

		tp.closePosition(position, exitPrice, tick.Timestamp, "EOD")
	}
}

// Process a single tick
func (tp *TickProcessor) processTick(tick *Tick) {
	// Store last tick for end-of-day closing
	tp.state.LastTick = tick

	// Check for new trading day
	if tp.isNewDay(tick) {
		tp.initializeNewDay(tick)
	}

	// Update session ranges
	tp.updateSessionRanges(tick)

	// Check for breakout opportunities
	tp.checkBreakouts(tick)

	// Update existing positions
	tp.updatePositions(tick)
}

// Process entire file with memory management
func (tp *TickProcessor) ProcessFile() error {
	file, err := os.Open(tp.instrument.FilePath)
	if err != nil {
		return fmt.Errorf("failed to open file %s: %v", tp.instrument.FilePath, err)
	}
	defer file.Close()

	// Use smaller buffer for memory efficiency
	reader := csv.NewReader(bufio.NewReaderSize(file, 4096))
	reader.FieldsPerRecord = -1 // Allow variable number of fields

	// Skip header if present
	if _, err := reader.Read(); err != nil && err != io.EOF {
		return fmt.Errorf("failed to read header: %v", err)
	}

	lineCount := 0

	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Printf("Error reading line %d: %v", lineCount, err)
			continue
		}

		tick, err := tp.parseTick(record)
		if err != nil {
			log.Printf("Error parsing tick at line %d: %v", lineCount, err)
			continue
		}

		tp.processTick(tick)
		lineCount++

		// Memory management - periodic cleanup
		if lineCount%100000 == 0 {
			tp.performMemoryCleanup()
		}

		// Progress reporting with memory stats
		if lineCount%1000000 == 0 {
			var m runtime.MemStats
			runtime.ReadMemStats(&m)
			log.Printf("Processed %d ticks for %s - Memory: %.2f MB",
				lineCount, tp.instrument.InstrumentCode, float64(m.Alloc)/1024/1024)
		}
	}

	// Final cleanup and close remaining positions
	if lineCount > 0 && tp.state.LastTick != nil {
		tp.closeAllPositions(tp.state.LastTick)
	}

	// Final memory cleanup
	tp.performMemoryCleanup()

	return tp.writeResults(lineCount)
}

// Perform memory cleanup
func (tp *TickProcessor) performMemoryCleanup() {
	// Clear tick buffer
	tp.tickBuffer = tp.tickBuffer[:0]

	// Remove inactive positions to prevent slice growth
	activePositions := make([]Position, 0, len(tp.state.Positions))
	for _, pos := range tp.state.Positions {
		if pos.IsActive {
			activePositions = append(activePositions, pos)
		}
	}
	tp.state.Positions = activePositions

	// Force garbage collection
	runtime.GC()
}

// Calculate strategy statistics from aggregated data
func (tp *TickProcessor) calculateStats() StrategyResult {
	winRate := 0.0
	if tp.stats.TotalTrades > 0 {
		winRate = float64(tp.stats.WinningTrades) / float64(tp.stats.TotalTrades) * 100
	}

	avgWin := 0.0
	if tp.stats.WinningTrades > 0 {
		avgWin = tp.stats.TotalWinPnL / float64(tp.stats.WinningTrades)
	}

	avgLoss := 0.0
	if tp.stats.LosingTrades > 0 {
		avgLoss = tp.stats.TotalLossPnL / float64(tp.stats.LosingTrades)
	}

	return StrategyResult{
		TotalTrades:   tp.stats.TotalTrades,
		WinningTrades: tp.stats.WinningTrades,
		LosingTrades:  tp.stats.LosingTrades,
		TotalPnL:      tp.stats.TotalPnL,
		MaxDrawdown:   tp.stats.MaxDrawdown,
		MaxProfit:     tp.stats.MaxProfit,
		WinRate:       winRate,
		AvgWin:        avgWin,
		AvgLoss:       avgLoss,
	}
}

// Write results to log file with memory usage info
func (tp *TickProcessor) writeResults(ticksProcessed int) error {
	stats := tp.calculateStats()

	// Get memory statistics
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	result := fmt.Sprintf(
		"INSTRUMENT: %s\n"+
			"FILE: %s\n"+
			"TICKS_PROCESSED: %d\n"+
			"TOTAL_TRADES: %d\n"+
			"WINNING_TRADES: %d\n"+
			"LOSING_TRADES: %d\n"+
			"WIN_RATE: %.2f%%\n"+
			"TOTAL_PNL: %.2f pips\n"+
			"AVG_WIN: %.2f pips\n"+
			"AVG_LOSS: %.2f pips\n"+
			"MAX_PROFIT: %.2f pips\n"+
			"MAX_DRAWDOWN: %.2f pips\n"+
			"MEMORY_USED: %.2f MB\n"+
			"PROCESSING_TIME: %s\n"+
			"----------------------------------------\n\n",
		tp.instrument.InstrumentCode,
		tp.instrument.FilePath,
		ticksProcessed,
		stats.TotalTrades,
		stats.WinningTrades,
		stats.LosingTrades,
		stats.WinRate,
		stats.TotalPnL,
		stats.AvgWin,
		stats.AvgLoss,
		stats.MaxProfit,
		stats.MaxDrawdown,
		float64(m.Alloc)/1024/1024, // Convert bytes to MB
		time.Now().Format("2006-01-02 15:04:05"),
	)

	_, err := tp.logFile.WriteString(result)
	if err != nil {
		return fmt.Errorf("failed to write results: %v", err)
	}

	log.Printf("Results written for %s - PnL: %.2f pips, Trades: %d, Memory: %.2f MB",
		tp.instrument.InstrumentCode, stats.TotalPnL, stats.TotalTrades, float64(m.Alloc)/1024/1024)

	return nil
}

// Load instruments from JSON file
func loadInstruments(configFile string) ([]Instrument, error) {
	data, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %v", err)
	}

	var instruments []Instrument
	if err := json.Unmarshal(data, &instruments); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %v", err)
	}

	return instruments, nil
}

// Main execution function
func main() {
	if len(os.Args) < 2 {
		log.Fatal("Usage: go run main.go <config_file.json>")
	}

	configFile := os.Args[1]
	logFile := "trading_results.log"

	// Load instrument configurations
	instruments, err := loadInstruments(configFile)
	if err != nil {
		log.Fatalf("Failed to load instruments: %v", err)
	}

	// Get strategy parameters
	params := getDefaultParams()

	// Create log file header
	logHeader := fmt.Sprintf(
		"TRADING STRATEGY BACKTEST RESULTS\n"+
			"Generated: %s\n"+
			"Strategy: Asian Range + London Breakout\n"+
			"Parameters:\n"+
			"- Asian Session: %02d:00 - %02d:00 UTC\n"+
			"- London Session: %02d:00 - %02d:00 UTC\n"+
			"- Breakout Buffer: %.1f pips\n"+
			"- Stop Loss: %.1f pips\n"+
			"- Take Profit: %.1f pips\n"+
			"- Max Positions: %d\n"+
			"========================================\n\n",
		time.Now().Format("2006-01-02 15:04:05"),
		params.AsianStartHour, params.AsianEndHour,
		params.LondonStartHour, params.LondonEndHour,
		params.BreakoutBuffer,
		params.StopLossPips,
		params.TakeProfitPips,
		params.MaxPositions,
	)

	// Write header to log file
	if err := os.WriteFile(logFile, []byte(logHeader), 0644); err != nil {
		log.Fatalf("Failed to create log file: %v", err)
	}

	log.Printf("Starting backtest for %d instruments...", len(instruments))

	// Process each instrument
	for i, instrument := range instruments {
		log.Printf("Processing %d/%d: %s", i+1, len(instruments), instrument.InstrumentCode)

		// Check if file exists
		if _, err := os.Stat(instrument.FilePath); os.IsNotExist(err) {
			log.Printf("File does not exist: %s", instrument.FilePath)
			continue
		}

		// Create processor for this instrument
		processor, err := NewTickProcessor(instrument, params, logFile)
		if err != nil {
			log.Printf("Failed to create processor for %s: %v", instrument.InstrumentCode, err)
			continue
		}

		// Process the file
		startTime := time.Now()
		if err := processor.ProcessFile(); err != nil {
			log.Printf("Failed to process %s: %v", instrument.InstrumentCode, err)
		} else {
			duration := time.Since(startTime)
			log.Printf("Completed %s in %v", instrument.InstrumentCode, duration)
		}

		processor.Close()
	}

	log.Printf("Backtest completed. Results saved to %s", logFile)
}
