# Trading Strategy Backtester

## Overview
A high-performance, memory-efficient Go-based trading strategy backtester that implements an Asian Range + London Breakout strategy on tick-level data.

## Key Features

### Memory Efficiency
- **Rolling Window Processing**: Processes data in temporal chunks rather than loading entire datasets
- **Aggregated Statistics**: Stores only essential metrics instead of individual trade details
- **Periodic Memory Cleanup**: Automatic garbage collection and buffer management
- **Streaming CSV Processing**: Uses small buffers to minimize memory footprint
- **Constant Memory Usage**: Memory usage remains stable regardless of dataset size

### Strategy Implementation
- **Asian Session Range Detection**: Identifies high/low ranges during Asian trading hours (00:00-08:00 UTC)
- **London Breakout Trading**: Executes trades when price breaks above/below Asian range during London session (08:00-16:00 UTC)
- **Risk Management**: Configurable stop loss and take profit levels
- **Position Sizing**: Standardized position sizes with proper pip value calculations

### Performance Monitoring
- **Real-time Memory Tracking**: Reports memory usage during processing
- **Progress Reporting**: Shows processing progress for large datasets
- **Detailed Statistics**: Win rate, average win/loss, drawdown metrics
- **Processing Speed**: Optimized for high-frequency tick data

## Architecture

### Core Components
1. **TickProcessor**: Main processing engine with memory management
2. **AggregatedStats**: Memory-efficient statistics storage
3. **DailyState**: Minimal daily trading state
4. **Position**: Individual trade tracking
5. **SessionRange**: Asian/London session range tracking

### Memory Optimizations
- Replaced growing slices with aggregated counters
- Implemented periodic memory cleanup (every 100,000 ticks)
- Use small CSV buffers (4KB) for streaming
- Clear inactive positions regularly
- Force garbage collection at strategic points

## Configuration

### Instrument Configuration (KeyFile.json)
```json
{
  "instrument_code": "EURUSD",
  "instrument_name": "Euro vs. US Dollar", 
  "category": "CURRENCIES",
  "file_path": "CSV-DATA/CURRENCIES/EURUSD_Ticks_2019.01.01_2025.08.03.csv",
  "data_structure": "Time (UTC),Ask,Bid,AskVolume,BidVolume",
  "quoting_decimal_places": 5,
  "effective_pip_value": 0.0001
}
```

### Strategy Parameters
- Asian Session: 00:00 - 08:00 UTC
- London Session: 08:00 - 16:00 UTC  
- Breakout Buffer: 2.0 pips
- Stop Loss: 20.0 pips
- Take Profit: 40.0 pips
- Max Positions: 2

## Usage

```bash
go run main.go KeyFile.json
```

## Output
Results are saved to `trading_results.log` with detailed statistics including:
- Total trades and win rate
- PnL metrics and drawdown
- Average win/loss amounts
- Memory usage statistics
- Processing time and performance

## Memory Performance
- Typical memory usage: < 1 MB for 100,000 ticks
- Constant memory footprint regardless of dataset size
- Efficient processing of multi-gigabyte tick files
- Automatic cleanup prevents memory leaks

## Links
- [Project Workarounds](ProjectWorkArounds.md)
- [Project Settings](ProjectSettings.md)
